# Diff Editor Action Buttons

## 功能说明

在 diff editor 的红绿装饰区域右下角添加了**接受**和**拒绝**两个悬浮按钮，仅在鼠标 hover 时显示。

## 支持的模式

### 1. Side-by-side 模式
- 左侧（原始）编辑器：显示删除内容的按钮
- 右侧（修改）编辑器：显示新增内容的按钮
- 每个差异区域独立显示按钮

### 2. Inline 模式
- 统一在修改后的编辑器中显示
- 绿色新增行：按钮显示在行尾
- 红色删除区域：按钮显示在 ViewZone 附近

## 按钮功能

### 接受按钮（✓）
- **功能**：保持当前修改状态
- **图标**：check
- **样式**：主要按钮样式（蓝色背景）

### 拒绝按钮（✕）
- **功能**：恢复到原始状态，调用 `diffEditor.revert(mapping)`
- **图标**：close
- **样式**：次要按钮样式（灰色背景）

## 实现文件

### 新增文件
- `components/diffActionButtonWidget.ts` - 按钮 Widget 实现

### 修改文件
- `diffEditorWidget.ts` - 集成按钮管理逻辑
- `style.css` - 添加按钮样式

## 技术实现

### 核心技术
- **ContentWidget**：利用编辑器内置的 Widget 系统，自动处理位置计算
- **Observable 响应式**：监听 diff 变化和鼠标事件
- **生命周期管理**：自动创建/销毁 Widget

### 事件处理
```typescript
// Hover 检测
editor.onMouseMove(e => {
    const diffId = this._getDiffIdAtPosition(e.target.position);
    this._hoveredDiff.set(diffId, undefined);
});

// 显示控制
autorun(reader => {
    const hoveredId = this._hoveredDiff.read(reader);
    for (const [widgetId, widget] of this._actionWidgets) {
        widget.setVisible(widgetId === hoveredId);
    }
});
```

## 测试方法

### 基本测试
1. 创建一个 diff editor
2. 设置有差异的文本模型
3. 鼠标悬停在红绿装饰区域
4. 观察按钮出现在**差异区域最后一行的右下角**
5. 点击测试按钮功能

### 位置准确性测试
1. 测试多行差异：按钮应出现在最后一行的右下角
2. 测试单行差异：按钮应出现在该行的右下角
3. 测试空行差异：按钮应出现在合适位置
4. 验证按钮不会遮挡文本内容

### 模式切换测试
1. 在 side-by-side 和 inline 模式间切换
2. 确保按钮在两种模式下都正确显示在右下角
3. 测试位置是否准确

### 交互测试
1. 测试 hover 进入/离开
2. 测试按钮点击功能
3. 测试拒绝按钮的 revert 功能

## 已修复的问题

### v1.2 简化定位
- **问题**：复杂的CSS偏移导致按钮不显示
- **修复**：
  - 移除CSS `transform` 偏移，回到简单定位
  - 简化位置计算逻辑
  - 使用 `ContentWidgetPositionPreference.EXACT` 直接定位
- **结果**：按钮现在能正常显示在行尾位置

### v1.1 位置修复（已回退）
- **问题**：按钮显示在左下角而不是右下角
- **尝试修复**：使用复杂的CSS transform定位
- **结果**：导致按钮不显示，已简化为基础定位方式

## 样式定制

可以通过 CSS 变量定制按钮外观：
```css
.monaco-editor .diff-action-buttons-widget {
    --button-accept-bg: var(--vscode-button-background);
    --button-reject-bg: var(--vscode-button-secondaryBackground);
}
```
