/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { IContentWidget, IContentWidgetPosition, ContentWidgetPositionPreference, ICodeEditor } from '../../../editorBrowser.js';
import { Position } from '../../../../common/core/position.js';
import { LineRangeMapping } from '../../../../common/diff/rangeMapping.js';
import { ThemeIcon } from '../../../../../base/common/themables.js';
import { Codicon } from '../../../../../base/common/codicons.js';
import type { DiffEditorWidget } from '../diffEditorWidget.js';

export class DiffActionButtonWidget implements IContentWidget {
	private readonly _domNode: HTMLElement;
	private _position: IContentWidgetPosition | null = null;
	// 是否可见
	private _isVisible = false;

	constructor(
		private readonly _id: string,
		private readonly _editor: ICodeEditor,
		private readonly _mapping: LineRangeMapping,
		private readonly _diffEditor: DiffEditorWidget
	) {
		this._domNode = this._createDomNode();
		this._editor.addContentWidget(this);
		this._calculateAndSetPosition();

		// 简单定位不需要监听滚动和布局变化
		// 因为使用的是相对于编辑器容器的 bottom 和 right 定位
	}

	getId(): string {
		return this._id;
	}

	getDomNode(): HTMLElement {
		return this._domNode;
	}

	getPosition(): IContentWidgetPosition | null {
		return this._position;
	}

	setVisible(visible: boolean): void {
		if (this._isVisible === visible) return;

		this._isVisible = visible;
		this._domNode.style.opacity = visible ? '1' : '0';
		this._domNode.style.pointerEvents = visible ? 'auto' : 'none';

		// 显示时确保位置正确
		if (visible) {
			this._setSimplePosition();
		}
	}

	private _createDomNode(): HTMLElement {
		const container = document.createElement('div');
		container.className = 'diff-action-buttons-widget';

		// 调试：添加明显的背景色
		container.style.cssText = `
			display: flex;
			gap: 4px;
			background: red;
			border: 2px solid blue;
			border-radius: 4px;
			padding: 4px;
			box-shadow: 0 2px 8px var(--vscode-widget-shadow);
			opacity: 1;
			transition: opacity 0.2s ease;
			pointer-events: auto;
			z-index: 1000;
		`;

		// 创建接受按钮
		const acceptBtn = this._createButton(
			Codicon.check,
			'接受此更改',
			'accept-button',
			() => this._handleAccept()
		);

		// 创建拒绝按钮
		const rejectBtn = this._createButton(
			Codicon.close,
			'拒绝此更改',
			'reject-button',
			() => this._handleReject()
		);

		container.appendChild(acceptBtn);
		container.appendChild(rejectBtn);

		// 防止按钮区域的鼠标事件影响编辑器
		container.addEventListener('mouseenter', (e) => {
			e.stopPropagation();
		});

		container.addEventListener('mouseleave', (e) => {
			e.stopPropagation();
		});

		return container;
	}

	private _createButton(icon: ThemeIcon, tooltip: string, className: string, onClick: () => void): HTMLElement {
		const button = document.createElement('button');
		button.className = `monaco-button ${className}`;
		button.style.cssText = `
			min-width: 24px;
			height: 24px;
			padding: 4px;
			border: none;
			border-radius: 3px;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			background: var(--vscode-button-background);
			color: var(--vscode-button-foreground);
		`;

		// 添加图标
		const iconSpan = document.createElement('span');
		iconSpan.className = ThemeIcon.asClassName(icon);
		iconSpan.style.fontSize = '12px';
		button.appendChild(iconSpan);

		// 添加tooltip
		button.title = tooltip;

		// 添加点击事件
		button.addEventListener('click', (e) => {
			e.preventDefault();
			e.stopPropagation();
			onClick();
		});

		// 添加hover效果
		button.addEventListener('mouseenter', () => {
			button.style.background = 'var(--vscode-button-hoverBackground)';
		});

		button.addEventListener('mouseleave', () => {
			button.style.background = 'var(--vscode-button-background)';
		});

		return button;
	}

	private _calculateAndSetPosition(): void {
		// 不使用ContentWidget的position，改用简单的CSS绝对定位
		this._position = null;
		this._editor.layoutContentWidget(this);

		// 使用简单的右下角定位
		this._setSimplePosition();
	}

	private _setSimplePosition(): void {
		// 简单的右下角定位
		this._domNode.style.position = 'absolute';
		this._domNode.style.bottom = '10px'; // 距离底部 10px
		this._domNode.style.right = '10px';  // 距离右侧 10px
		this._domNode.style.zIndex = '1000'; // 确保在最上层

		console.log('Simple position: bottom: 10px, right: 10px');
	}

	private _handleAccept(): void {
		// 接受更改：保持当前修改状态
		console.log('接受更改:', this._mapping);
		// 可以在这里添加具体的接受逻辑，比如标记为已接受
		// 或者触发特定的事件通知外部组件
	}

	private _handleReject(): void {
		// 拒绝更改：恢复到原始状态
		console.log('拒绝更改:', this._mapping);
		this._diffEditor.revert(this._mapping);
	}

	dispose(): void {
		this._editor.removeContentWidget(this);
	}
}
